import '../../statefulbusinesslogic/core/models/activity_status_model.dart';

/// Repository interface for managing user's activity status settings
/// 
/// This repository handles the persistence and retrieval of user's activity status preferences.
/// Following the four-layer dependency rule, this interface is defined in the repository layer
/// and implemented in the provider layer.
abstract class ActivityStatusRepository {
  /// Get the current activity status settings for a user
  /// Returns null if no settings are found (defaults should be applied)
  Future<ActivityStatusModel?> getActivityStatus(String userId);

  /// Save the activity status settings for a user
  /// Returns true if successful, false otherwise
  Future<bool> saveActivityStatus(ActivityStatusModel activityStatus);

  /// Delete the activity status settings for a user
  /// Returns true if successful, false otherwise
  Future<bool> deleteActivityStatus(String userId);

  /// Check if activity status settings exist for a user
  Future<bool> hasActivityStatus(String userId);
}
