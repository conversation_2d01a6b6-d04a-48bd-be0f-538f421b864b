import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:provider/provider.dart';

import '../../../statefulbusinesslogic/bloc/friends/friends_bloc.dart';
import '../../../statefulbusinesslogic/bloc/friends/friends_event.dart';
import '../../../statefulbusinesslogic/bloc/friends/friends_state.dart';
import '../../../statefulbusinesslogic/core/notifiers/nav_bar_visibility_notifier.dart';
import '../../widgets/friends_tile.dart';
import '../../widgets/gradient_background.dart';
import '../../widgets/main_app_bar.dart';
import '../chat/chat_page.dart';
import '../unified_profile_page/unified_profile_page.dart';

class FriendsPage extends StatefulWidget {
  const FriendsPage({super.key});

  @override
  State<FriendsPage> createState() => _FriendsPageState();
}

class _FriendsPageState extends State<FriendsPage> {
  late NavBarVisibilityNotifier _navBarNotifier;

  @override
  void initState() {
    super.initState();
    _navBarNotifier = Provider.of<NavBarVisibilityNotifier>(
      context,
      listen: false,
    );

    // Load friends data when the page initializes
    context.read<FriendsBloc>().add(const LoadFriendsEvent());
  }

  @override
  Widget build(BuildContext context) => GradientBackground(
    child: Scaffold(
      backgroundColor: Colors.transparent,
      body: Stack(
        children: [
          // Scrollable content layer (below title and app bar)
          SafeArea(
            bottom: false,
            child: Padding(
              padding: const EdgeInsets.only(
                top: 80, // Space for app bar + title
              ),
              child: BlocBuilder<FriendsBloc, FriendsState>(
                builder: (context, state) => _buildFriendsContent(state),
              ),
            ),
          ),
          // Floating app bar layer (above content)
          const Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: MainAppBar(),
          ),
          // Floating title layer (above content, below app bar)
          const Positioned(
            top: 64, // Height of MainAppBar
            left: 0,
            right: 0,
            child: Center(
              child: Padding(
                padding: EdgeInsets.symmetric(vertical: 16),
                child: Text(
                  'Friends',
                  style: TextStyle(
                    color: Color(0xFF00FFFF), // Cyan
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    ),
  );

  Widget _buildFriendsContent(FriendsState state) {
    if (state.status == FriendsStatus.loading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (state.status == FriendsStatus.error) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.grey[600]),
            const SizedBox(height: 16),
            Text(
              'Failed to load friends',
              style: TextStyle(fontSize: 18, color: Colors.grey[600]),
            ),
            const SizedBox(height: 8),
            Text(
              state.errorMessage ?? 'Unknown error',
              style: TextStyle(fontSize: 14, color: Colors.grey[500]),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                context.read<FriendsBloc>().add(const LoadFriendsEvent());
              },
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    final friends = state.filteredFriends;

    if (friends.isEmpty) {
      return const Center(
        child: Text(
          "You don't have any friends yet :)\nComplete a bubble to make true friends",
          style: TextStyle(color: Colors.grey),
          textAlign: TextAlign.center,
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        context.read<FriendsBloc>().add(const LoadFriendsEvent());
      },
      child: ListView.builder(
        padding: const EdgeInsets.only(
          left: 20,
          right: 20,
          bottom: kBottomNavigationBarHeight + 20.0,
        ),
        itemCount: friends.length,
        itemBuilder: (context, index) {
          final friendMap = friends[index];
          return FriendsTile(
            name:
                '${friendMap['firstName'] as String? ?? ''} ${friendMap['lastName'] as String? ?? ''}'
                    .trim(),
            subtitle: friendMap['username'] as String? ?? 'unknown',
            avatarUrl: friendMap['profilePictureUrl'] as String?,
            isOnline: friendMap['isOnline'] as bool? ?? false,
            onTap: () {
              _showFriendProfile(friendMap['id'] as String);
            },
          );
        },
      ),
    );
  }

  void _showFriendProfile(String friendId) {
    _navBarNotifier.setNavBarVisible(false);
    showDialog(
      context: context,
      builder: (context) => UnifiedProfilePage(userId: friendId),
    ).then((_) => _navBarNotifier.setNavBarVisible(true));
  }
}
