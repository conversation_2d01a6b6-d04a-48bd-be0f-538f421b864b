import 'package:bloc/bloc.dart';
import '../../../repositories/activity_status/activity_status_repository.dart';
import '../../core/models/activity_status_model.dart';
import '../../core/services/logging_service.dart';
import 'activity_status_event.dart';
import 'activity_status_state.dart';

/// BLoC for managing user's activity status settings
/// 
/// This BLoC handles:
/// - Loading activity status settings from repository
/// - Updating activity status preferences
/// - Managing state during updates
/// 
/// Follows the four-layer dependency rule by only depending on the repository interface.
class ActivityStatusBloc extends Bloc<ActivityStatusEvent, ActivityStatusState> {
  ActivityStatusBloc({
    required ActivityStatusRepository activityStatusRepository,
  }) : _activityStatusRepository = activityStatusRepository,
       super(const ActivityStatusInitial()) {
    
    on<LoadActivityStatusEvent>(_onLoadActivityStatus);
    on<UpdateActivityStatusEvent>(_onUpdateActivityStatus);
    on<ResetActivityStatusEvent>(_onResetActivityStatus);
  }

  final ActivityStatusRepository _activityStatusRepository;

  /// Load activity status settings for a user
  Future<void> _onLoadActivityStatus(
    LoadActivityStatusEvent event,
    Emitter<ActivityStatusState> emit,
  ) async {
    emit(const ActivityStatusLoading());

    try {
      LoggingService.info('ActivityStatusBloc: Loading activity status for user ${event.userId}');
      
      // Try to load from repository
      final activityStatus = await _activityStatusRepository.getActivityStatus(event.userId);
      
      if (activityStatus != null) {
        LoggingService.info('ActivityStatusBloc: Loaded activity status from storage');
        emit(ActivityStatusLoaded(activityStatus: activityStatus));
      } else {
        // Create default settings for the user
        final defaultStatus = ActivityStatusModel(userId: event.userId);
        LoggingService.info('ActivityStatusBloc: Created default activity status settings');
        
        // Save default settings
        final saved = await _activityStatusRepository.saveActivityStatus(defaultStatus);
        if (saved) {
          emit(ActivityStatusLoaded(activityStatus: defaultStatus));
        } else {
          emit(const ActivityStatusError(
            message: 'Failed to save default activity status settings',
          ));
        }
      }
    } catch (e, stackTrace) {
      LoggingService.error('ActivityStatusBloc: Error loading activity status: $e', stackTrace: stackTrace);
      emit(ActivityStatusError(
        message: 'Failed to load activity status settings',
        technicalDetails: e.toString(),
      ));
    }
  }

  /// Update activity status setting
  Future<void> _onUpdateActivityStatus(
    UpdateActivityStatusEvent event,
    Emitter<ActivityStatusState> emit,
  ) async {
    final currentState = state;
    if (currentState is! ActivityStatusLoaded) {
      LoggingService.warning('ActivityStatusBloc: Cannot update activity status - not in loaded state');
      return;
    }

    emit(ActivityStatusUpdating(currentStatus: currentState.activityStatus));

    try {
      LoggingService.info('ActivityStatusBloc: Updating activity status to ${event.showActivityStatus} for user ${event.userId}');
      
      final updatedStatus = currentState.activityStatus.copyWith(
        showActivityStatus: event.showActivityStatus,
      );

      final saved = await _activityStatusRepository.saveActivityStatus(updatedStatus);

      if (saved) {
        emit(ActivityStatusUpdated(
          activityStatus: updatedStatus,
          message: event.showActivityStatus 
              ? 'Activity status is now visible to others'
              : 'Activity status is now hidden from others',
        ));

        // Return to loaded state
        emit(ActivityStatusLoaded(activityStatus: updatedStatus));
      } else {
        emit(const ActivityStatusError(
          message: 'Failed to save activity status settings',
        ));
        // Return to previous state
        emit(ActivityStatusLoaded(activityStatus: currentState.activityStatus));
      }
    } catch (e, stackTrace) {
      LoggingService.error('ActivityStatusBloc: Error updating activity status: $e', stackTrace: stackTrace);
      emit(ActivityStatusError(
        message: 'Failed to update activity status',
        technicalDetails: e.toString(),
      ));
      // Return to previous state
      emit(ActivityStatusLoaded(activityStatus: currentState.activityStatus));
    }
  }

  /// Reset activity status to default (show activity)
  Future<void> _onResetActivityStatus(
    ResetActivityStatusEvent event,
    Emitter<ActivityStatusState> emit,
  ) async {
    final currentState = state;
    if (currentState is! ActivityStatusLoaded) {
      LoggingService.warning('ActivityStatusBloc: Cannot reset activity status - not in loaded state');
      return;
    }

    emit(ActivityStatusUpdating(currentStatus: currentState.activityStatus));

    try {
      LoggingService.info('ActivityStatusBloc: Resetting activity status to defaults for user ${event.userId}');
      
      final defaultStatus = ActivityStatusModel(userId: event.userId);
      final saved = await _activityStatusRepository.saveActivityStatus(defaultStatus);

      if (saved) {
        emit(ActivityStatusUpdated(
          activityStatus: defaultStatus,
          message: 'Activity status reset to default (visible to others)',
        ));

        // Return to loaded state
        emit(ActivityStatusLoaded(activityStatus: defaultStatus));
      } else {
        emit(const ActivityStatusError(
          message: 'Failed to reset activity status settings',
        ));
        // Return to previous state
        emit(ActivityStatusLoaded(activityStatus: currentState.activityStatus));
      }
    } catch (e, stackTrace) {
      LoggingService.error('ActivityStatusBloc: Error resetting activity status: $e', stackTrace: stackTrace);
      emit(ActivityStatusError(
        message: 'Failed to reset activity status',
        technicalDetails: e.toString(),
      ));
      // Return to previous state
      emit(ActivityStatusLoaded(activityStatus: currentState.activityStatus));
    }
  }
}
