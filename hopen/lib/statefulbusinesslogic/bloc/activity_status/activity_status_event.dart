import 'package:equatable/equatable.dart';

/// Events for ActivityStatusBloc
abstract class ActivityStatusEvent extends Equatable {
  const ActivityStatusEvent();

  @override
  List<Object?> get props => [];
}

/// Load activity status settings for a user
class LoadActivityStatusEvent extends ActivityStatusEvent {
  const LoadActivityStatusEvent({required this.userId});

  final String userId;

  @override
  List<Object?> get props => [userId];
}

/// Update the activity status setting (show/hide)
class UpdateActivityStatusEvent extends ActivityStatusEvent {
  const UpdateActivityStatusEvent({
    required this.userId,
    required this.showActivityStatus,
  });

  final String userId;
  final bool showActivityStatus;

  @override
  List<Object?> get props => [userId, showActivityStatus];
}

/// Reset activity status to default (show activity)
class ResetActivityStatusEvent extends ActivityStatusEvent {
  const ResetActivityStatusEvent({required this.userId});

  final String userId;

  @override
  List<Object?> get props => [userId];
}
