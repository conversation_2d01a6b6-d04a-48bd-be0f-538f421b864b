import 'package:equatable/equatable.dart';
import '../../core/models/activity_status_model.dart';

/// States for ActivityStatusBloc
abstract class ActivityStatusState extends Equatable {
  const ActivityStatusState();

  @override
  List<Object?> get props => [];
}

/// Initial state when activity status hasn't been loaded yet
class ActivityStatusInitial extends ActivityStatusState {
  const ActivityStatusInitial();
}

/// Loading state when fetching activity status settings
class ActivityStatusLoading extends ActivityStatusState {
  const ActivityStatusLoading();
}

/// Successfully loaded activity status settings
class ActivityStatusLoaded extends ActivityStatusState {
  const ActivityStatusLoaded({
    required this.activityStatus,
  });

  final ActivityStatusModel activityStatus;

  @override
  List<Object?> get props => [activityStatus];
}

/// Error state when loading or updating activity status fails
class ActivityStatusError extends ActivityStatusState {
  const ActivityStatusError({
    required this.message,
    this.technicalDetails,
  });

  final String message;
  final String? technicalDetails;

  @override
  List<Object?> get props => [message, technicalDetails];
}

/// State when activity status is being updated
class ActivityStatusUpdating extends ActivityStatusState {
  const ActivityStatusUpdating({
    required this.currentStatus,
  });

  final ActivityStatusModel currentStatus;

  @override
  List<Object?> get props => [currentStatus];
}

/// Successfully updated activity status
class ActivityStatusUpdated extends ActivityStatusState {
  const ActivityStatusUpdated({
    required this.activityStatus,
    this.message,
  });

  final ActivityStatusModel activityStatus;
  final String? message;

  @override
  List<Object?> get props => [activityStatus, message];
}
