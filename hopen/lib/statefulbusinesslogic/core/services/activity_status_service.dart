import 'dart:async';
import 'package:flutter/material.dart';
import '../models/activity_status_model.dart';
import 'logging_service.dart';

/// Service that manages the computed online visibility based on app lifecycle and user preferences
/// 
/// This service combines:
/// 1. App lifecycle state (foreground/background)
/// 2. User's activity status preference (show/hide)
/// 
/// The user appears online ONLY when:
/// - The app is in the foreground (actively being used)
/// - AND the user has NOT disabled activity status
/// 
/// This service belongs to the Business Logic Layer and follows the four-layer dependency rule.
class ActivityStatusService with WidgetsBindingObserver {
  static ActivityStatusService? _instance;
  static ActivityStatusService get instance {
    _instance ??= ActivityStatusService._internal();
    return _instance!;
  }

  ActivityStatusService._internal();

  // Stream controllers for status updates
  final StreamController<bool> _visibilityController = StreamController<bool>.broadcast();
  final StreamController<AppLifecycleState> _lifecycleController = StreamController<AppLifecycleState>.broadcast();

  // Current state
  AppLifecycleState _currentLifecycleState = AppLifecycleState.resumed;
  ActivityStatusModel? _activityStatusSettings;
  bool _isInitialized = false;

  /// Stream of online status visibility changes
  Stream<bool> get visibilityStream => _visibilityController.stream;

  /// Stream of app lifecycle state changes
  Stream<AppLifecycleState> get lifecycleStream => _lifecycleController.stream;

  /// Current computed online status visibility
  bool get isOnlineVisible => _computeOnlineVisibility();

  /// Current app lifecycle state
  AppLifecycleState get currentLifecycleState => _currentLifecycleState;

  /// Current activity status settings
  ActivityStatusModel? get activityStatusSettings => _activityStatusSettings;

  /// Initialize the service
  Future<void> initialize() async {
    if (_isInitialized) {
      LoggingService.info('ActivityStatusService: Already initialized');
      return;
    }

    LoggingService.info('ActivityStatusService: Initializing');

    // Register as app lifecycle observer
    WidgetsBinding.instance.addObserver(this);

    _isInitialized = true;
    LoggingService.info('ActivityStatusService: Initialization complete');

    // Emit initial state
    _emitVisibilityUpdate();
  }

  /// Dispose the service
  void dispose() {
    LoggingService.info('ActivityStatusService: Disposing');
    
    WidgetsBinding.instance.removeObserver(this);
    _visibilityController.close();
    _lifecycleController.close();
    
    _isInitialized = false;
  }

  /// Update activity status settings
  void updateActivityStatusSettings(ActivityStatusModel? settings) {
    LoggingService.info('ActivityStatusService: Updating activity status settings');
    _activityStatusSettings = settings;
    _emitVisibilityUpdate();
  }

  /// Handle app lifecycle state changes
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    LoggingService.info('ActivityStatusService: App lifecycle state changed to $state');
    
    final previousState = _currentLifecycleState;
    _currentLifecycleState = state;
    
    // Emit lifecycle change
    _lifecycleController.add(state);
    
    // Update visibility based on new lifecycle state
    _emitVisibilityUpdate();

    // Log important transitions
    if (previousState == AppLifecycleState.paused && state == AppLifecycleState.resumed) {
      LoggingService.info('ActivityStatusService: App returned to foreground - user may appear online');
    } else if (previousState == AppLifecycleState.resumed && state == AppLifecycleState.paused) {
      LoggingService.info('ActivityStatusService: App moved to background - user will appear offline');
    }
  }

  /// Compute whether the user should appear online
  bool _computeOnlineVisibility() {
    // Must be in foreground
    final isInForeground = _currentLifecycleState == AppLifecycleState.resumed;
    
    // Must have activity status enabled (default to true if no settings)
    final showActivityStatus = _activityStatusSettings?.showActivityStatus ?? true;

    final isVisible = isInForeground && showActivityStatus;

    LoggingService.debug('ActivityStatusService: Computing visibility - '
        'foreground: $isInForeground, '
        'showActivity: $showActivityStatus, '
        'result: $isVisible');

    return isVisible;
  }

  /// Emit visibility update to listeners
  void _emitVisibilityUpdate() {
    final isVisible = _computeOnlineVisibility();
    _visibilityController.add(isVisible);
    
    LoggingService.info('ActivityStatusService: Emitted visibility update - visible: $isVisible');
  }

  /// Force refresh the activity status
  void refresh() {
    LoggingService.info('ActivityStatusService: Force refreshing activity status');
    _emitVisibilityUpdate();
  }

  /// Check if the app is currently in foreground
  bool get isAppInForeground => _currentLifecycleState == AppLifecycleState.resumed;

  /// Check if activity status is hidden by user preference
  bool get isActivityStatusHidden => !(_activityStatusSettings?.showActivityStatus ?? true);

  /// Get the current show activity status preference
  bool get showActivityStatus => _activityStatusSettings?.showActivityStatus ?? true;
}
