import 'package:equatable/equatable.dart';

/// Domain model for user's activity status settings
/// 
/// This model represents the user's preference for showing their online activity.
/// By default, activity status is shown to everyone (except blocked users).
/// Users can toggle this to hide their activity from everyone.
class ActivityStatusModel extends Equatable {
  const ActivityStatusModel({
    required this.userId,
    this.showActivityStatus = true, // Default: show activity to everyone
  });

  final String userId;
  final bool showActivityStatus; // true = show to all, false = hide from all

  /// Create a copy with updated values
  ActivityStatusModel copyWith({
    String? userId,
    bool? showActivityStatus,
  }) {
    return ActivityStatusModel(
      userId: userId ?? this.userId,
      showActivityStatus: showActivityStatus ?? this.showActivityStatus,
    );
  }

  /// Create from JSON
  factory ActivityStatusModel.fromJson(Map<String, dynamic> json) {
    return ActivityStatusModel(
      userId: json['userId'] as String,
      showActivityStatus: json['showActivityStatus'] as bool? ?? true,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'showActivityStatus': showActivityStatus,
    };
  }

  @override
  List<Object?> get props => [userId, showActivityStatus];

  @override
  String toString() {
    return 'ActivityStatusModel(userId: $userId, showActivityStatus: $showActivityStatus)';
  }
}
