import '../../../repositories/activity_status/activity_status_repository.dart';
import '../../../statefulbusinesslogic/core/models/activity_status_model.dart';
import '../../../repositories/local_storage/user_settings_repository.dart';
import '../../../statefulbusinesslogic/core/services/logging_service.dart';

/// Implementation of ActivityStatusRepository using local storage
/// 
/// This implementation stores activity status settings in local storage using
/// the UserSettingsRepository. It follows the four-layer dependency rule by
/// implementing the repository interface defined in the repository layer.
class ActivityStatusRepositoryImpl implements ActivityStatusRepository {
  ActivityStatusRepositoryImpl({
    required UserSettingsRepository userSettingsRepository,
  }) : _userSettingsRepository = userSettingsRepository;

  final UserSettingsRepository _userSettingsRepository;
  
  // Storage key for activity status settings
  static const String _activityStatusKey = 'activity_status_settings';

  @override
  Future<ActivityStatusModel?> getActivityStatus(String userId) async {
    try {
      LoggingService.info('ActivityStatusRepository: Loading activity status for user $userId');
      
      final settingsJson = await _userSettingsRepository.getSetting<Map<String, dynamic>>(_activityStatusKey);
      
      if (settingsJson != null) {
        final activityStatus = ActivityStatusModel.fromJson(settingsJson);
        
        // Verify the userId matches
        if (activityStatus.userId == userId) {
          LoggingService.info('ActivityStatusRepository: Found activity status settings for user $userId');
          return activityStatus;
        } else {
          LoggingService.warning('ActivityStatusRepository: UserId mismatch in stored settings');
        }
      }
      
      LoggingService.info('ActivityStatusRepository: No activity status settings found for user $userId');
      return null;
    } catch (e, stackTrace) {
      LoggingService.error('ActivityStatusRepository: Error loading activity status: $e', stackTrace: stackTrace);
      return null;
    }
  }

  @override
  Future<bool> saveActivityStatus(ActivityStatusModel activityStatus) async {
    try {
      LoggingService.info('ActivityStatusRepository: Saving activity status for user ${activityStatus.userId}');
      
      await _userSettingsRepository.saveSetting(_activityStatusKey, activityStatus.toJson());
      
      LoggingService.info('ActivityStatusRepository: Successfully saved activity status settings');
      return true;
    } catch (e, stackTrace) {
      LoggingService.error('ActivityStatusRepository: Error saving activity status: $e', stackTrace: stackTrace);
      return false;
    }
  }

  @override
  Future<bool> deleteActivityStatus(String userId) async {
    try {
      LoggingService.info('ActivityStatusRepository: Deleting activity status for user $userId');
      
      // For this implementation, we'll save a default settings object instead of deleting
      // This ensures consistent behavior
      final defaultSettings = ActivityStatusModel(userId: userId);
      await _userSettingsRepository.saveSetting(_activityStatusKey, defaultSettings.toJson());
      
      LoggingService.info('ActivityStatusRepository: Successfully reset activity status to defaults');
      return true;
    } catch (e, stackTrace) {
      LoggingService.error('ActivityStatusRepository: Error deleting activity status: $e', stackTrace: stackTrace);
      return false;
    }
  }

  @override
  Future<bool> hasActivityStatus(String userId) async {
    try {
      final activityStatus = await getActivityStatus(userId);
      return activityStatus != null;
    } catch (e) {
      LoggingService.error('ActivityStatusRepository: Error checking activity status existence: $e');
      return false;
    }
  }
}
