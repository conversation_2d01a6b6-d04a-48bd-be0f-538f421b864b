import 'dart:convert';
import '../../../repositories/local_storage/user_settings_repository.dart';
import '../../../statefulbusinesslogic/core/services/logging_service.dart';
import '../../services/local_storage/local_storage_service.dart';

/// Implementation of UserSettingsRepository using SharedPreferences
/// 
/// This implementation stores user settings in local storage using SharedPreferences
/// through the LocalStorageService. It follows the four-layer dependency rule by
/// implementing the repository interface defined in the repository layer.
class UserSettingsRepositoryImpl implements UserSettingsRepository {
  UserSettingsRepositoryImpl({
    required LocalStorageService localStorageService,
  }) : _localStorageService = localStorageService;

  final LocalStorageService _localStorageService;
  
  // Prefix for user settings keys to avoid conflicts
  static const String _keyPrefix = 'user_settings_';

  @override
  Future<T?> getSetting<T>(String key) async {
    try {
      final prefixedKey = _keyPrefix + key;
      LoggingService.info('UserSettingsRepository: Loading setting for key $prefixedKey');

      // Handle different types using appropriate LocalStorageService methods
      if (T == String) {
        final value = await _localStorageService.getString(prefixedKey);
        return value as T?;
      } else if (T == bool) {
        final value = await _localStorageService.getBool(prefixedKey);
        return value as T?;
      } else if (T == int) {
        final value = await _localStorageService.getInt(prefixedKey);
        return value as T?;
      } else if (T == double) {
        final value = await _localStorageService.getDouble(prefixedKey);
        return value as T?;
      } else if (T == Map<String, dynamic>) {
        final value = await _localStorageService.getString(prefixedKey);
        if (value != null) {
          try {
            return jsonDecode(value) as T;
          } catch (e) {
            LoggingService.error('UserSettingsRepository: Error parsing JSON for key $prefixedKey: $e');
            return null;
          }
        }
        return null;
      } else {
        // For other types, try to get as string and return as-is
        final value = await _localStorageService.getString(prefixedKey);
        return value as T?;
      }
    } catch (e, stackTrace) {
      LoggingService.error('UserSettingsRepository: Error loading setting for key $key: $e', stackTrace: stackTrace);
      return null;
    }
  }

  @override
  Future<bool> saveSetting<T>(String key, T value) async {
    try {
      final prefixedKey = _keyPrefix + key;
      LoggingService.info('UserSettingsRepository: Saving setting for key $prefixedKey');

      // Use appropriate LocalStorageService method based on type
      if (value is String) {
        await _localStorageService.saveString(prefixedKey, value);
      } else if (value is bool) {
        await _localStorageService.saveBool(prefixedKey, value);
      } else if (value is int) {
        await _localStorageService.saveInt(prefixedKey, value);
      } else if (value is double) {
        await _localStorageService.saveDouble(prefixedKey, value);
      } else if (value is Map<String, dynamic>) {
        final jsonString = jsonEncode(value);
        await _localStorageService.saveString(prefixedKey, jsonString);
      } else {
        // For other types, convert to string
        await _localStorageService.saveString(prefixedKey, value.toString());
      }

      LoggingService.info('UserSettingsRepository: Successfully saved setting for key $prefixedKey');
      return true;
    } catch (e, stackTrace) {
      LoggingService.error('UserSettingsRepository: Error saving setting for key $key: $e', stackTrace: stackTrace);
      return false;
    }
  }

  @override
  Future<bool> deleteSetting(String key) async {
    try {
      final prefixedKey = _keyPrefix + key;
      LoggingService.info('UserSettingsRepository: Deleting setting for key $prefixedKey');
      
      await _localStorageService.remove(prefixedKey);
      
      LoggingService.info('UserSettingsRepository: Successfully deleted setting for key $prefixedKey');
      return true;
    } catch (e, stackTrace) {
      LoggingService.error('UserSettingsRepository: Error deleting setting for key $key: $e', stackTrace: stackTrace);
      return false;
    }
  }

  @override
  Future<bool> hasSetting(String key) async {
    try {
      final prefixedKey = _keyPrefix + key;
      final value = await _localStorageService.containsKey(prefixedKey);
      return value;
    } catch (e) {
      LoggingService.error('UserSettingsRepository: Error checking setting existence for key $key: $e');
      return false;
    }
  }

  @override
  Future<bool> clearAllSettings() async {
    try {
      LoggingService.info('UserSettingsRepository: Clearing all user settings');
      
      final allKeys = await getAllKeys();
      for (final key in allKeys) {
        await _localStorageService.remove(key);
      }
      
      LoggingService.info('UserSettingsRepository: Successfully cleared all user settings');
      return true;
    } catch (e, stackTrace) {
      LoggingService.error('UserSettingsRepository: Error clearing all settings: $e', stackTrace: stackTrace);
      return false;
    }
  }

  @override
  Future<List<String>> getAllKeys() async {
    try {
      final allKeys = await _localStorageService.getAllKeys();
      return allKeys.where((key) => key.startsWith(_keyPrefix)).toList();
    } catch (e) {
      LoggingService.error('UserSettingsRepository: Error getting all keys: $e');
      return [];
    }
  }
}
