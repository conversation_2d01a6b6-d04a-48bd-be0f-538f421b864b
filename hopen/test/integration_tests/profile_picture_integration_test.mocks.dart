// Mocks generated by <PERSON><PERSON><PERSON> 5.4.5 from annotations
// in hopen/test/integration_tests/profile_picture_integration_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i5;

import 'package:hopen/repositories/auth/auth_repository.dart' as _i2;
import 'package:hopen/repositories/profile_picture/profile_picture_repository.dart'
    as _i9;
import 'package:hopen/statefulbusinesslogic/core/error/result.dart' as _i6;
import 'package:hopen/statefulbusinesslogic/core/models/profile_picture_result.dart'
    as _i3;
import 'package:hopen/statefulbusinesslogic/core/models/user_model.dart' as _i7;
import 'package:hopen/statefulbusinesslogic/core/usecases/signup_usecase.dart'
    as _i4;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i8;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeAuthRepository_0 extends _i1.SmartFake
    implements _i2.AuthRepository {
  _FakeAuthRepository_0(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeProfilePictureResult_1 extends _i1.SmartFake
    implements _i3.ProfilePictureResult {
  _FakeProfilePictureResult_1(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

class _FakeProfilePictureValidationResult_2 extends _i1.SmartFake
    implements _i3.ProfilePictureValidationResult {
  _FakeProfilePictureValidationResult_2(
    Object parent,
    Invocation parentInvocation,
  ) : super(
          parent,
          parentInvocation,
        );
}

/// A class which mocks [SignUpUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockSignUpUseCase extends _i1.Mock implements _i4.SignUpUseCase {
  MockSignUpUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.AuthRepository get repository => (super.noSuchMethod(
        Invocation.getter(#repository),
        returnValue: _FakeAuthRepository_0(
          this,
          Invocation.getter(#repository),
        ),
      ) as _i2.AuthRepository);

  @override
  _i5.Future<_i6.Result<_i7.UserModel>> call(_i4.SignUpParams? params) =>
      (super.noSuchMethod(
        Invocation.method(
          #call,
          [params],
        ),
        returnValue: _i5.Future<_i6.Result<_i7.UserModel>>.value(
            _i8.dummyValue<_i6.Result<_i7.UserModel>>(
          this,
          Invocation.method(
            #call,
            [params],
          ),
        )),
      ) as _i5.Future<_i6.Result<_i7.UserModel>>);
}

/// A class which mocks [ProfilePictureRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockProfilePictureRepository extends _i1.Mock
    implements _i9.ProfilePictureRepository {
  MockProfilePictureRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i5.Future<_i3.ProfilePictureResult> pickFromGallery() => (super.noSuchMethod(
        Invocation.method(
          #pickFromGallery,
          [],
        ),
        returnValue: _i5.Future<_i3.ProfilePictureResult>.value(
            _FakeProfilePictureResult_1(
          this,
          Invocation.method(
            #pickFromGallery,
            [],
          ),
        )),
      ) as _i5.Future<_i3.ProfilePictureResult>);

  @override
  _i5.Future<_i3.ProfilePictureResult> takePhoto() => (super.noSuchMethod(
        Invocation.method(
          #takePhoto,
          [],
        ),
        returnValue: _i5.Future<_i3.ProfilePictureResult>.value(
            _FakeProfilePictureResult_1(
          this,
          Invocation.method(
            #takePhoto,
            [],
          ),
        )),
      ) as _i5.Future<_i3.ProfilePictureResult>);

  @override
  _i5.Future<bool> removeProfilePicture(String? imageUrl) =>
      (super.noSuchMethod(
        Invocation.method(
          #removeProfilePicture,
          [imageUrl],
        ),
        returnValue: _i5.Future<bool>.value(false),
      ) as _i5.Future<bool>);

  @override
  _i5.Future<_i3.ProfilePictureValidationResult> validateImage(
          String? imagePath) =>
      (super.noSuchMethod(
        Invocation.method(
          #validateImage,
          [imagePath],
        ),
        returnValue: _i5.Future<_i3.ProfilePictureValidationResult>.value(
            _FakeProfilePictureValidationResult_2(
          this,
          Invocation.method(
            #validateImage,
            [imagePath],
          ),
        )),
      ) as _i5.Future<_i3.ProfilePictureValidationResult>);

  @override
  _i5.Future<String?> uploadLocalProfilePicture(String? localPath) =>
      (super.noSuchMethod(
        Invocation.method(
          #uploadLocalProfilePicture,
          [localPath],
        ),
        returnValue: _i5.Future<String?>.value(),
      ) as _i5.Future<String?>);
}
